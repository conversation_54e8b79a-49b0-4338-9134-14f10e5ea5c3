<template>
	<view class="loading">
		<z-paging ref="paging" v-model="dataList" @query="queryList" bgColor="#f7f7f7"
			:refresher-status.sync="refresherStatus">
			<view slot="top">
				<cl-navbar title="实名认证" mpWeiXinShow :autoBack="true" :fixed="false" class="custom_navbar"> </cl-navbar>
			</view>
			<custom-refresher slot="refresher" :status="refresherStatus"></custom-refresher>
			<view class="loading_list">
				<view class="form_body">
					<view class="form_item">
						<view class="item_label">
							<text class="required">*</text>
							<text>*姓名</text>
						</view>
						<view class="item_content">
							<input class="input_field" placeholderStyle="color: #BFBFBF;" fontSize="28rpx" color="#333"
								placeholder="请输入金额" v-model="form.name" />
							<u-icon name="arrow-right" size="20"></u-icon>
						</view>
					</view>
				</view>
			</view>
			<view slot="bottom">
			</view>
		</z-paging>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				refresherStatus: 0,
				dataList: [],
			};
		},
		computed: {},
		onLoad() {},
		methods: {
			queryList(pageNo, pageSize) {
				let params = {
					page: pageNo,
					page_size: pageSize,
				};
				// this.$api.getNews.getNewsList(params).then((res) => {
				// 	if (res.code == 200) {
				// 		this.$refs.paging.complete(res.result.data);
				// 		// this.$refs.paging.completeByNoMore(res.result, true); //:refresher-enabled="false" :show-loading-more-no-more-view="false" 
				// 	} else {
				// 		this.$refs.paging.complete(false);
				// 	}
				// });
			},
		},
	};
</script>

<style lang="scss" scoped>
	.loading {
		width: 100%;

		.loading_list {
			width: 100%;

			.form_body {
				padding: 0 25rpx;
				background-color: #fff;

				.form_item {
					width: 100%;
					background-color: #FFFFFF;
					@include flex-center(row, space-between, center);
					padding: 25rpx;
					box-sizing: border-box;

					.item_label {
						@include flex-center(row, flex-start, center);
						font-size: 28rpx;
						font-weight: bold;
						color: #333333;
					}

					.item_content {
						@include flex-center(row, flex-end, center);
						gap: 20rpx;
						font-size: 28rpx;
						color: #333333;

						.u-icon {
							margin-left: 10rpx;
						}
					}
				}
			}

		}
	}
</style>