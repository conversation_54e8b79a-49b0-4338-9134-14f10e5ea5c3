<template>
	<view class="loading">
		<z-paging ref="paging" v-model="dataList" @query="queryList" bgColor="#f7f7f7"
			:refresher-status.sync="refresherStatus">
			<view slot="top">
				<cl-navbar title="房贷计算器" mpWeiXinShow :autoBack="true" :fixed="false" class="custom_navbar">
				</cl-navbar>
				<!-- 贷款类型选择 -->
				<view class="loan_type_section">
					<u-tabs :list="tabList" :scrollable="false" :inactiveStyle="inactiveStyle"
						:activeStyle="activeStyle" lineWidth="50rpx" :lineColor="baseColor" lineHeight="6rpx"
						@click="tabClick"></u-tabs>
				</view>
			</view>
			<custom-refresher slot="refresher" :status="refresherStatus"></custom-refresher>
			<view class="loading_list">
				<view class="calculator_container" v-if="loanType === '商业贷款'">
					<!-- 计算方式选择 -->
					<view class="calculation_method_section form_section">
						<view class="form_row">
							<view class="label_text">计算方式</view>
							<view class="method_options">
								<view class="method_option" @click="selectMethod('total')">
									<view class="radio_button" :class="{ active: form1.type === 'total' }">
										<view class="radio_inner" v-if="form1.type === 'total'"></view>
									</view>
									<view class="method_text">按贷款总额</view>
								</view>
								<view class="method_option" @click="selectMethod('price')">
									<view class="radio_button" :class="{ active: form1.type === 'price' }">
										<view class="radio_inner" v-if="form1.type === 'price'"></view>
									</view>
									<view class="method_text">按房屋总价</view>
								</view>
							</view>
						</view>
						<view class="form_row" v-if="form1.type === 'price'">
							<view class="label_text">房屋总价</view>
							<view class="input_area">
								<input class="input_field" placeholderStyle="color: #BFBFBF;" fontSize="28rpx"
									color="#333" placeholder="请输入金额" v-model="form1.sumprice" />
								<view class="unit_text">万</view>
							</view>
						</view>
						<view class="form_row" v-if="form1.type === 'price'">
							<view class="label_text">贷款比例</view>
							<view class="input_area">
								<input class="input_field" placeholderStyle="color: #BFBFBF;" fontSize="28rpx"
									color="#333" placeholder="请输入金额" v-model="form1.bl" />
								<view class="unit_text">成</view>
							</view>
						</view>
						<view class="form_row">
							<view class="label_text">商贷金额</view>
							<view class="input_area">
								<input class="input_field" placeholderStyle="color: #BFBFBF;" fontSize="28rpx"
									color="#333" placeholder="请输入金额" v-model="form1.loanAmount" />
								<view class="unit_text">万</view>
							</view>
						</view>
					</view>
					<!-- 商贷金额 -->
					<view class="form_section">

						<view class="form_row">
							<view class="label_text">商贷年限</view>
							<view class="input_area" @click="selectLoanYears">
								<view class="input_value" :class="{ inactive: !form1.loanYears }">{{ form1.loanYears ||
									'请选择' }}年</view>
								<u-icon name="arrow-right" color="#333333" size="28rpx"></u-icon>
							</view>
						</view>
						<view class="form_row">
							<view class="label_text">利率方式</view>
							<view class="input_area" @click="selectRateType">
								<view class="input_value" :class="{ inactive: !form1.rateType }">{{ form1.rateType ||
									'请选择' }}</view>
								<u-icon name="arrow-right" color="#333333" size="28rpx"></u-icon>
							</view>
						</view>
						<view class="form_row">
							<view class="label_text">商贷利率</view>
							<view class="input_area">
								<input class="input_field" placeholderStyle="color: #BFBFBF;" fontSize="28rpx"
									color="#333" placeholder="请输入" type="number" v-model="form1.interestRate" />
								<view class="unit_text">%</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view slot="bottom" class="bottom">
				<view class="calculate_button" @click="calculate">
					开始计算
				</view>
			</view>
		</z-paging>
	</view>
</template>

<script>
export default {
	data() {
		return {
			refresherStatus: 0,
			dataList: [],
			// 表单数据
			loanType: '商业贷款', // 贷款类型：商业贷款, 公积金贷款, 组合贷款
			form1: {
				sumn_price: "",
				bl: '6.5',
				type: 'total', // 计算方式：total-按贷款总额, price-按房屋总价
				loanAmount: '', // 贷款金额
				loanYears: "", // 贷款年限
				rateType: '', // 利率方式
				interestRate: '', // 利率
			},
			tabList: [
				{ name: '商业贷款' },
				{ name: '公积金贷款' },
				{ name: '组合贷款' }
			],
			inactiveStyle: {
				fontSize: '30rpx',
				fontWeight: 'bold',
				color: '#333333',
			},
			activeStyle: {
				color: '#006AFC',
				fontSize: '30rpx',
				fontWeight: 'bold',
			},
		};
	},
	computed: {},
	onLoad() { },
	methods: {
		queryList(pageNo, pageSize) {
			let params = {
				page: pageNo,
				page_size: pageSize,
			};

			// this.$api.getNews.getNewsList(params).then((res) => {
			// 	if (res.code == 200) {
			// 		this.$refs.paging.complete(res.result.data);
			// 		// this.$refs.paging.completeByNoMore(res.result, true); //:refresher-enabled="false" :show-loading-more-no-more-view="false"
			// 	} else {
			// 		this.$refs.paging.complete(false);
			// 	}
			// });
		},
		// 切换标签页
		tabClick(e) {
			this.loanType = e.name;

		},
		// 选择计算方式
		selectMethod(method) {
			this.form1.type = method;
		},
		// 选择贷款年限
		selectLoanYears() {
			console.log('选择贷款年限');
		},
		// 选择利率方式
		selectRateType() {
			console.log('选择利率方式');
		},
		// 开始计算
		calculate() {
			console.log('开始计算');
			uni.showToast({
				title: '计算完成',
				icon: 'success'
			});
		}
	},
};
</script>

<style lang="scss" scoped>
.loading {
	width: 100%;

	.loan_type_section {
		background: #ffffff;
	}

	.loading_list {
		width: 100%;
		padding: 20rpx 0;

		.calculator_container {
			width: 100%;
			@include flex_center(column, null, null);
			gap: 20rpx;

			>view {
				width: 1005;
			}

			// 贷款类型选择


			// 计算方式选择区域
			.calculation_method_section {
				background: #ffffff;
				padding: 0 25rpx;

				&:last-child {
					.form_row {
						border: none
					}
				}

				.form_row {
					@include flex-center(row, space-between, center);
					padding: 30rpx 0;
					border-bottom: 1rpx solid #EEEEEE;

					&:last-child {

						border-bottom: 0;
					}

					.label_text {
						font-size: 28rpx;
						font-weight: bold;
						color: #333333;
						line-height: 28rpx;
					}

					.method_options {
						@include flex-center(row, flex-end, center);
						gap: 60rpx;

						.method_option {
							@include flex-center(row, center, center);
							gap: 16rpx;

							.radio_button {
								width: 32rpx;
								height: 32rpx;
								border-radius: 50%;
								background: #F5F5F5;
								@include flex-center(row, center, center);

								&.active {
									background: rgba(0, 106, 252, 0.18);
								}

								.radio_inner {
									width: 16rpx;
									height: 16rpx;
									border-radius: 50%;
									background: #006AFC;
								}
							}

							.method_text {
								font-size: 28rpx;
								color: #333333;
								line-height: 28rpx;
							}
						}
					}
				}


			}

			// 表单区域
			.form_section {
				background: #ffffff;
				padding: 0 25rpx;

				.form_row {
					@include flex-center(row, space-between, center);
					padding: 30rpx 0;
					border-bottom: 1rpx solid #EEEEEE;

					&:last-child {
						border-bottom: none;
					}

					.label_text {
						font-size: 28rpx;
						font-weight: bold;
						color: #333333;
						line-height: 28rpx;
					}

					.input_area {
						@include flex-center(row, flex-end, center);
						gap: 20rpx;
						flex: 1;

						.input_field {
							flex: 1;
							text-align: right;
							color: #333333;
							font-size: 28rpx;

							&::placeholder {
								color: #BFBFBF;
								font-size: 28rpx;
							}
						}

						.input_value {
							color: #333333;
							font-size: 28rpx;

							&.inactive {
								color: #BFBFBF;
							}
						}

						.unit_text {
							color: #333333;
							font-size: 28rpx;
							padding-bottom: 8rpx
						}
					}
				}


			}


		}
	}

	// 计算按钮
	.bottom {
		padding: 20rpx 25rpx;
		background-color: #ffffff;

		.calculate_button {
			width: 100%;
			height: 88rpx;
			background: #006AFC;
			border-radius: 8rpx;
			@include flex-center(row, center, center);
			color: #ffffff;
			font-size: 30rpx;
		}
	}

}
</style>