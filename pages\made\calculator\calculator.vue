<template>
	<view class="loading">
		<z-paging ref="paging" v-model="dataList" @query="queryList" bgColor="#f7f7f7"
			:refresher-status.sync="refresherStatus">
			<view slot="top">
				<cl-navbar title="房贷计算器" mpWeiXinShow :autoBack="true" :fixed="false" class="custom_navbar">
				</cl-navbar>
			</view>
			<custom-refresher slot="refresher" :status="refresherStatus"></custom-refresher>
			<view class="loading_list">
				<view v-for="(item, i) in dataList" :key="i">
					111
				</view>
			</view>
			<view slot="bottom">
			</view>
		</z-paging>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				refresherStatus: 0,
				dataList: [],
			};
		},
		computed: {},
		onLoad() {},
		methods: {
			queryList(pageNo, pageSize) {
				let params = {
					page: pageNo,
					page_size: pageSize,
				};
				// this.$api.getNews.getNewsList(params).then((res) => {
				// 	if (res.code == 200) {
				// 		this.$refs.paging.complete(res.result.data);
				// 		// this.$refs.paging.completeByNoMore(res.result, true); //:refresher-enabled="false" :show-loading-more-no-more-view="false" 
				// 	} else {
				// 		this.$refs.paging.complete(false);
				// 	}
				// });
			},
		},
	};
</script>

<style lang="scss" scoped>
	.loading {
		width: 100%;

		.loading_list {
			width: 100%;
		}
	}
</style>