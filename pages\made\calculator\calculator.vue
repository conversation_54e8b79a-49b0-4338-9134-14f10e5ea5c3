<template>
	<view class="loading">
		<z-paging ref="paging" v-model="dataList" @query="queryList" bgColor="#f7f7f7"
			:refresher-status.sync="refresherStatus">
			<view slot="top">
				<cl-navbar title="房贷计算器" mpWeiXinShow :autoBack="true" :fixed="false" class="custom_navbar">
				</cl-navbar>
				<!-- 贷款类型选择 -->
				<view class="loan_type_section">
					<u-tabs :list="tabList" :scrollable="false" :inactiveStyle="inactiveStyle"
						:activeStyle="activeStyle" lineWidth="50rpx" :lineColor="baseColor" lineHeight="6rpx"
						@click="tabClick"></u-tabs>
				</view>
			</view>
			<custom-refresher slot="refresher" :status="refresherStatus"></custom-refresher>
			<view class="loading_list">
				<view class="calculator_container" v-if="loanType === '商业贷款'">
					<!-- 计算方式选择 -->
					<view class="calculation_method_section form_section">
						<view class="form_row">
							<view class="label_text">计算方式</view>
							<view class="method_options">
								<view class="method_option" @click="selectMethod('total')">
									<view class="radio_button" :class="{ active: form1.type === 'total' }">
										<view class="radio_inner" v-if="form1.type === 'total'"></view>
									</view>
									<view class="method_text">按贷款总额</view>
								</view>
								<view class="method_option" @click="selectMethod('price')">
									<view class="radio_button" :class="{ active: form1.type === 'price' }">
										<view class="radio_inner" v-if="form1.type === 'price'"></view>
									</view>
									<view class="method_text">按房屋总价</view>
								</view>
							</view>
						</view>
						<view class="form_row" v-if="form1.type === 'price'">
							<view class="label_text">房屋总价</view>
							<view class="input_area">
								<input class="input_field" placeholderStyle="color: #BFBFBF;" fontSize="28rpx"
									color="#333" placeholder="请输入金额" v-model="form1.sumprice" />
								<view class="unit_text">万</view>
							</view>
						</view>
						<view class="form_row" v-if="form1.type === 'price'">
							<view class="label_text">贷款比例</view>
							<view class="input_area">
								<input class="input_field" placeholderStyle="color: #BFBFBF;" fontSize="28rpx"
									color="#333" placeholder="请输入金额" v-model="form1.bl" />
								<view class="unit_text">成</view>
							</view>
						</view>
						<view class="form_row">
							<view class="label_text">商贷金额</view>
							<view class="input_area">
								<input class="input_field" placeholderStyle="color: #BFBFBF;" fontSize="28rpx"
									color="#333" placeholder="请输入金额" v-model="form1.loanAmount" />
								<view class="unit_text">万</view>
							</view>
						</view>
					</view>

					<view class="form_section">

						<view class="form_row">
							<view class="label_text">商贷年限</view>
							<view class="input_area" @click="openPicker('loanYears', 'form1', 'loanYears')">
								<view class="input_value">{{ form1.loanYears }}年</view>

								<u-icon name="arrow-right" color="#333333" size="28rpx"></u-icon>
							</view>
						</view>
						<view class="form_row">
							<view class="label_text">利率方式</view>
							<view class="input_area" @click="openPicker('rateType', 'form1', 'rateType')">
								<view class="input_value">{{ form1.rateType }}</view>
								<u-icon name="arrow-right" color="#333333" size="28rpx"></u-icon>
							</view>
						</view>
						<view class="form_row">
							<view class="label_text">商贷利率</view>
							<view class="input_area">
								<input class="input_field" placeholderStyle="color: #BFBFBF;" fontSize="28rpx"
									color="#333" placeholder="请输入" type="number" v-model="form1.interestRate" />
								<view class="unit_text">%</view>
							</view>
						</view>
					</view>
				</view>
				<view class="calculator_container" v-if="loanType === '公积金贷款'">
					<!-- 计算方式选择 -->
					<view class="calculation_method_section form_section">
						<view class="form_row">
							<view class="label_text">计算方式</view>
							<view class="method_options">
								<view class="method_option" @click="selectMethod2('total')">
									<view class="radio_button" :class="{ active: form2.type === 'total' }">
										<view class="radio_inner" v-if="form2.type === 'total'"></view>
									</view>
									<view class="method_text">按贷款总额</view>
								</view>
								<view class="method_option" @click="selectMethod2('price')">
									<view class="radio_button" :class="{ active: form2.type === 'price' }">
										<view class="radio_inner" v-if="form2.type === 'price'"></view>
									</view>
									<view class="method_text">按房屋总价</view>
								</view>
							</view>
						</view>

						<!-- 按房屋总价时显示的字段 -->
						<view class="form_row" v-if="form2.type === 'price'">
							<view class="label_text">房屋总价</view>
							<view class="input_area">
								<input class="input_field" placeholderStyle="color: #BFBFBF;" fontSize="28rpx"
									color="#333" placeholder="请输入金额" v-model="form2.housePrice" />
								<view class="unit_text">万</view>
							</view>
						</view>
						<view class="form_row" v-if="form2.type === 'price'">
							<view class="label_text">贷款比例</view>
							<view class="input_area">
								<input class="input_field" placeholderStyle="color: #BFBFBF;" fontSize="28rpx"
									color="#333" placeholder="请输入比例" v-model="form2.loanRatio" />
								<view class="unit_text">成</view>
							</view>
						</view>

						<!-- 按贷款总额时显示的字段 -->
						<view class="form_row" v-if="form2.type === 'total'">
							<view class="label_text">贷款总额</view>
							<view class="input_area">
								<input class="input_field" placeholderStyle="color: #BFBFBF;" fontSize="28rpx"
									color="#333" placeholder="请输入金额" v-model="form2.loanAmount" />
								<view class="unit_text">万</view>
							</view>
						</view>

						<!-- 公积金金额 -->
						<view class="form_row">
							<view class="label_text">公积金金额</view>
							<view class="input_area">
								<input class="input_field" placeholderStyle="color: #BFBFBF;" fontSize="28rpx"
									color="#333" placeholder="请输入金额" v-model="form2.fundAmount" />
								<view class="unit_text">万</view>
							</view>
						</view>
					</view>

					<!-- 公积金相关字段 -->
					<view class="form_section">
						<view class="form_row">
							<view class="label_text">公积金年限</view>
							<view class="input_area" @click="openPicker('loanYears', 'form2', 'fundYears')">
								<input class="input_field" placeholderStyle="color: #BFBFBF;" fontSize="28rpx"
									color="#333" placeholder="请输入年限" v-model="form2.fundYears" />
								<view class="unit_text">年</view>
								<view class="input_value">{{ form2.fundYears }}年</view>
								<u-icon name="arrow-right" color="#333333" size="28rpx"></u-icon>
							</view>
						</view>
						<view class="form_row">
							<view class="label_text">公积金利率</view>
							<view class="input_area" @click="openPicker('fundRate', 'form2', 'fundRate')">
								<view class="input_value">{{ form2.fundRate }}</view>
								<u-icon name="arrow-right" color="#333333" size="28rpx"></u-icon>
							</view>
						</view>
					</view>

					<!-- 商贷相关字段 -->
					<view class="form_section">
						<view class="form_row">
							<view class="label_text">商贷金额</view>
							<view class="input_area" @click="selectBusinessAmount">
								<input class="input_field" placeholderStyle="color: #BFBFBF;" fontSize="28rpx"
									color="#333" placeholder="请输入金额" v-model="form2.businessAmount" />
								<view class="unit_text">万</view>
							</view>
						</view>
						<view class="form_row">
							<view class="label_text">商贷年限</view>
							<view class="input_area" @click="openPicker('loanYears', 'form2', 'businessYears')">
								<view class="input_value">{{ form2.businessYears }}年</view>
								<u-icon name="arrow-right" color="#333333" size="28rpx"></u-icon>
							</view>
						</view>
						<view class="form_row">
							<view class="label_text">利率方式</view>
							<view class="input_area" @click="openPicker('rateType', 'form2', 'rateType')">
								<view class="input_value">{{ form2.rateType }}</view>
								<u-icon name="arrow-right" color="#333333" size="28rpx"></u-icon>
							</view>
						</view>
						<view class="form_row">
							<view class="label_text">商贷利率</view>
							<view class="input_area">
								<input class="input_field" placeholderStyle="color: #BFBFBF;" fontSize="28rpx"
									color="#333" placeholder="请输入" type="number" v-model="form2.businessRate" />
								<view class="unit_text">%</view>
							</view>
						</view>
					</view>
				</view>
				<view class="calculator_container" v-if="loanType === '组合贷款'">
					<!-- 计算方式选择 -->
					<view class="calculation_method_section form_section">
						<view class="form_row">
							<view class="label_text">计算方式</view>
							<view class="method_options">
								<view class="method_option" @click="selectMethod3('total')">
									<view class="radio_button" :class="{ active: form3.type === 'total' }">
										<view class="radio_inner" v-if="form3.type === 'total'"></view>
									</view>
									<view class="method_text">按贷款总额</view>
								</view>
								<view class="method_option" @click="selectMethod3('price')">
									<view class="radio_button" :class="{ active: form3.type === 'price' }">
										<view class="radio_inner" v-if="form3.type === 'price'"></view>
									</view>
									<view class="method_text">按房屋总价</view>
								</view>
							</view>
						</view>

						<!-- 按房屋总价时显示的字段 -->
						<view class="form_row" v-if="form3.type === 'price'">
							<view class="label_text">房屋总价</view>
							<view class="input_area">
								<input class="input_field" placeholderStyle="color: #BFBFBF;" fontSize="28rpx"
									color="#333" placeholder="请输入金额" v-model="form3.housePrice" />
								<view class="unit_text">万</view>
							</view>
						</view>
						<view class="form_row" v-if="form3.type === 'price'">
							<view class="label_text">贷款比例</view>
							<view class="input_area">
								<input class="input_field" placeholderStyle="color: #BFBFBF;" fontSize="28rpx"
									color="#333" placeholder="请输入比例" v-model="form3.loanRatio" />
								<view class="unit_text">成</view>
							</view>
						</view>


					</view>

					<!-- 公积金相关字段 -->
					<view class="form_section">
						<!-- 公积金金额 -->
						<view class="form_row">
							<view class="label_text">公积金金额</view>
							<view class="input_area">
								<input class="input_field" placeholderStyle="color: #BFBFBF;" fontSize="28rpx"
									color="#333" placeholder="请输入金额" v-model="form3.fundAmount" />
								<view class="unit_text">万</view>
							</view>
						</view>
						<view class="form_row">
							<view class="label_text">公积金年限</view>
							<view class="input_area">
								<input class="input_field" placeholderStyle="color: #BFBFBF;" fontSize="28rpx"
									color="#333" placeholder="请输入年限" v-model="form3.fundYears" />
								<view class="unit_text">年</view>

							</view>
						</view>
						<view class="form_row">
							<view class="label_text">公积金利率</view>
							<view class="input_area" @click="openPicker('fundRate', 'form3', 'fundRate')">
								<view class="input_value">{{ form3.fundRate }}</view>
								<u-icon name="arrow-right" color="#333333" size="28rpx"></u-icon>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view slot="bottom" class="bottom">
				<view class="calculate_button" @click="calculate">
					开始计算
				</view>
			</view>
		</z-paging>

		<!-- 通用选择器 -->
		<u-picker :show="pickerShow" :columns="getPickerColumns()" :defaultIndex="getPickerIndex()"
			@confirm="confirmPicker" @cancel="pickerShow = false" :title="getPickerTitle()">
		</u-picker>
	</view>
</template>

<script>
export default {
	data() {
		return {
			refresherStatus: 0,
			dataList: [],
			// 表单数据
			loanType: '商业贷款', // 贷款类型：商业贷款, 公积金贷款, 组合贷款
			form1: {
				sumn_price: "",
				bl: '6.5',
				type: 'total', // 计算方式：total-按贷款总额, price-按房屋总价
				loanAmount: '', // 贷款金额
				loanYears: "30", // 贷款年限
				rateType: '按LPR', // 利率方式
				interestRate: '', // 利率
			},
			form2: {
				type: 'total', // 计算方式：total-按贷款总额, price-按房屋总价
				loanAmount: '', // 贷款总额
				fundAmount: '', // 公积金金额
				fundYears: '', // 公积金年限
				fundRate: '3.25%', // 公积金利率
				businessAmount: '', // 商贷金额
				businessYears: '30', // 商贷年限
				rateType: '按LPR', // 利率方式
				businessRate: '3.5', // 商贷利率
				housePrice: '', // 房屋总价
				loanRatio: '6.5', // 贷款比例
			},
			form3: {
				type: 'total', // 计算方式：total-按贷款总额, price-按房屋总价
				fundAmount: '', // 公积金金额
				fundYears: '', // 公积金年限
				fundRate: '3.25%', // 公积金利率
				housePrice: '', // 房屋总价
				loanRatio: '6.5', // 贷款比例
			},
			// 通用选择器数据
			pickerShow: false,
			pickerType: '', // 当前选择器类型
			currentForm: '', // 当前操作的表单 form1/form2/form3
			currentField: '', // 当前操作的字段
			// 贷款年限选择器 (2025年标准)
			loanYearsList: ['1年', '2年', '3年', '5年', '8年', '10年', '15年', '20年', '25年', '30年'],
			loanYearsIndex: 9, // 默认30年
			// 利率方式选择器 (2025年标准)
			rateTypeList: ['按LPR浮动', '固定利率', '按基准利率浮动'],
			rateTypeIndex: 0, // 默认按LPR浮动
			// 公积金利率选择器 (2025年最新利率)
			fundRateList: ['3.1%', '3.25%', '3.35%', '3.5%'],
			fundRateIndex: 0, // 默认3.1%,
			tabList: [
				{ name: '商业贷款' },
				{ name: '公积金贷款' },
				{ name: '组合贷款' }
			],
			inactiveStyle: {
				fontSize: '30rpx',
				fontWeight: 'bold',
				color: '#333333',
			},
			activeStyle: {
				color: '#006AFC',
				fontSize: '30rpx',
				fontWeight: 'bold',
			},
		};
	},
	computed: {},
	onLoad() { },
	methods: {
		queryList(pageNo, pageSize) {
			let params = {
				page: pageNo,
				page_size: pageSize,
			};

			// this.$api.getNews.getNewsList(params).then((res) => {
			// 	if (res.code == 200) {
			// 		this.$refs.paging.complete(res.result.data);
			// 		// this.$refs.paging.completeByNoMore(res.result, true); //:refresher-enabled="false" :show-loading-more-no-more-view="false"
			// 	} else {
			// 		this.$refs.paging.complete(false);
			// 	}
			// });
		},
		// 切换标签页
		tabClick(e) {
			this.loanType = e.name;
		},
		// 选择计算方式
		selectMethod(method) {
			this.form1.type = method;
		},
		// 选择公积金贷款计算方式
		selectMethod2(method) {
			this.form2.type = method;
		},
		// 选择组合贷款计算方式
		selectMethod3(method) {
			this.form3.type = method;
		},
		// 通用选择器方法
		openPicker(type, form, field) {
			this.pickerType = type;
			this.currentForm = form;
			this.currentField = field;
			this.pickerShow = true;
		},
		// 获取选择器列数据
		getPickerColumns() {
			switch (this.pickerType) {
				case 'loanYears':
					return [this.loanYearsList];
				case 'rateType':
					return [this.rateTypeList];
				case 'fundRate':
					return [this.fundRateList];
				default:
					return [[]];
			}
		},
		// 获取选择器默认索引
		getPickerIndex() {
			switch (this.pickerType) {
				case 'loanYears':
					return [this.loanYearsIndex];
				case 'rateType':
					return [this.rateTypeIndex];
				case 'fundRate':
					return [this.fundRateIndex];
				default:
					return [0];
			}
		},
		// 获取选择器标题
		getPickerTitle() {
			switch (this.pickerType) {
				case 'loanYears':
					return '选择贷款年限';
				case 'rateType':
					return '选择利率方式';
				case 'fundRate':
					return '选择公积金利率';
				default:
					return '请选择';
			}
		},
		// 确认选择器选择
		confirmPicker(e) {
			const { indexs, value } = e;
			const selectedValue = value[0];

			// 更新对应表单的字段值
			this[this.currentForm][this.currentField] = selectedValue;

			// 更新对应的索引
			switch (this.pickerType) {
				case 'loanYears':
					this.loanYearsIndex = indexs[0];
					break;
				case 'rateType':
					this.rateTypeIndex = indexs[0];
					break;
				case 'fundRate':
					this.fundRateIndex = indexs[0];
					break;
			}

			this.pickerShow = false;
		},
		// 开始计算
		calculate() {
			console.log('开始计算');
			uni.showToast({
				title: '计算完成',
				icon: 'success'
			});
		}
	},
};
</script>

<style lang="scss" scoped>
.loading {
	width: 100%;

	.loan_type_section {
		background: #ffffff;
	}

	.loading_list {
		width: 100%;
		padding: 20rpx 0;

		.calculator_container {
			width: 100%;
			@include flex_center(column, null, null);
			gap: 20rpx;

			>view {
				width: 1005;
			}

			// 贷款类型选择


			// 计算方式选择区域
			.calculation_method_section {
				background: #ffffff;
				padding: 0 25rpx;

				&:last-child {
					.form_row {
						border: none
					}
				}

				.form_row {
					@include flex-center(row, space-between, center);
					padding: 30rpx 0;
					border-bottom: 1rpx solid #EEEEEE;

					&:last-child {

						border-bottom: 0;
					}

					.label_text {
						font-size: 28rpx;
						font-weight: bold;
						color: #333333;
						line-height: 28rpx;
					}

					.method_options {
						@include flex-center(row, flex-end, center);
						gap: 60rpx;

						.method_option {
							@include flex-center(row, center, center);
							gap: 16rpx;

							.radio_button {
								width: 32rpx;
								height: 32rpx;
								border-radius: 50%;
								background: #F5F5F5;
								@include flex-center(row, center, center);

								&.active {
									background: rgba(0, 106, 252, 0.18);
								}

								.radio_inner {
									width: 16rpx;
									height: 16rpx;
									border-radius: 50%;
									background: #006AFC;
								}
							}

							.method_text {
								font-size: 28rpx;
								color: #333333;
								line-height: 28rpx;
							}
						}
					}
				}


			}

			// 表单区域
			.form_section {
				background: #ffffff;
				padding: 0 25rpx;

				.form_row {
					@include flex-center(row, space-between, center);
					padding: 30rpx 0;
					border-bottom: 1rpx solid #EEEEEE;

					&:last-child {
						border-bottom: none;
					}

					.label_text {
						font-size: 28rpx;
						font-weight: bold;
						color: #333333;
						line-height: 28rpx;
					}

					.input_area {
						@include flex-center(row, flex-end, center);
						gap: 20rpx;
						flex: 1;

						.input_field {
							flex: 1;
							text-align: right;
							color: #333333;
							font-size: 28rpx;

							&::placeholder {
								color: #BFBFBF;
								font-size: 28rpx;
							}
						}

						.input_value {
							color: #333333;
							font-size: 28rpx;

							&.inactive {
								color: #BFBFBF;
							}
						}

						.unit_text {
							color: #333333;
							font-size: 28rpx;
							padding-bottom: 4rpx
						}
					}
				}


			}


		}
	}

	// 计算按钮
	.bottom {
		padding: 20rpx 25rpx;
		background-color: #ffffff;

		.calculate_button {
			width: 100%;
			height: 88rpx;
			background: #006AFC;
			border-radius: 8rpx;
			@include flex-center(row, center, center);
			color: #ffffff;
			font-size: 30rpx;
		}
	}

}
</style>