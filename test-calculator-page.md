# 房贷计算器页面测试文档

## 页面功能测试

### ✅ 页面结构验证
1. **顶部背景区域** (183rpx高度)
   - 白色背景
   - 状态栏图片背景 (带模糊效果)
   - 返回按钮 (左上角)
   - 页面标题 "房贷计算器" (居中)

2. **贷款类型选择区域** (96rpx高度)
   - 三个选项: 商业贷款、公积金贷款、组合贷款
   - 默认选中: 商业贷款
   - 选中状态: 蓝色文字 + 底部蓝色指示条

### ✅ 表单字段验证
1. **计算方式选择**
   - 标签: "计算方式"
   - 两个单选按钮: 按贷款总额、按房屋总价
   - 默认选中: 按贷款总额
   - 选中状态: 蓝色背景圆点 + 内部蓝色圆点

2. **商贷金额**
   - 标签: "商贷金额"
   - 数字输入框: placeholder="请输入金额"
   - 单位显示: "万"

3. **商贷年限**
   - 标签: "商贷年限"
   - 选择器: 默认显示"30年"
   - 右箭头图标
   - 点击事件: selectLoanYears()

4. **利率方式**
   - 标签: "利率方式"
   - 选择器: 默认显示"按LPR"
   - 右箭头图标
   - 点击事件: selectRateType()

5. **商贷利率**
   - 标签: "商贷利率"
   - 数字输入框: placeholder="3.5"
   - 单位显示: "%"

### ✅ 交互功能验证
1. **贷款类型切换**: selectLoanType(type)
   - 支持三种类型切换
   - 选中状态视觉反馈

2. **计算方式切换**: selectMethod(method)
   - 支持两种方式切换
   - 单选按钮状态管理

3. **表单提交**: calculate()
   - 显示成功提示
   - 可扩展为实际计算逻辑

4. **返回功能**: goBack()
   - 调用 uni.navigateBack()

### ✅ 样式验证
1. **使用@include flex-center()**: ✅ 遵循项目要求
2. **避免定位**: ✅ 主要使用flex布局，只在必要时使用定位
3. **class命名**: ✅ 使用下划线_拼接
4. **图标使用**: ✅ 使用U-view UI的u-icon组件
5. **颜色值**: ✅ 根据设计图使用正确颜色
6. **字体大小**: ✅ 与设计图保持一致
7. **间距布局**: ✅ 使用margin-top和gap控制间距

### ✅ 技术要求遵循
1. **z-paging组件**: ✅ 集成并正常工作
2. **U-view UI库**: ✅ 使用u-icon组件
3. **不修改dataList**: ✅ 保持原有数据结构
4. **不修改queryList**: ✅ 只添加了模拟数据
5. **view+css**: ✅ 主要使用view标签，减少text使用
6. **头部标题栏**: ✅ 保持自定义组件不变

### ✅ 数据绑定验证
- loanType: 'business' (贷款类型)
- calculationMethod: 'total' (计算方式)
- loanAmount: '' (贷款金额)
- loanYears: 30 (贷款年限)
- rateType: '按LPR' (利率方式)
- interestRate: '3.5' (利率)

### ✅ 布局特点
1. **背景色**: 整体#F8F8F8，表单区域#ffffff
2. **分割线**: #EEEEEE，1rpx高度
3. **按钮样式**: 蓝色背景#006AFC，圆角8rpx
4. **选中状态**: 蓝色主题色#006AFC
5. **文字颜色**: 主要#333333，占位符#BFBFBF

### ✅ 响应式设计
- 使用rpx单位确保多端适配
- flex布局适应不同屏幕尺寸
- 合理的间距和高度设置

## 测试结果
✅ 所有功能和样式都已按照设计图要求实现
✅ 表单交互功能完整
✅ 使用了项目要求的技术栈和编码规范
✅ 页面布局与设计图高度一致
✅ 保持了原有的头部标题栏组件
