<template>
	<view class="loading">
		<z-paging ref="paging" v-model="dataList" @query="queryList" @scroll="onScroll" bgColor="#fff"
			:refresher-status.sync="refresherStatus">
			<view slot="top">
				<cl-navbar title="详情" :leftIconColor="!is_bgChange ? '#fff' : ''"
					:titleStyle="{ 'color': !is_bgChange ? '#fff' : '' }" mpWeiXinShow :autoBack="true" :fixed="true"
					:bgColor="!is_bgChange ? 'transparent' : '#fff'" class="custom_navbar">
					<view slot="right">
						<u-icon :name="rightIcon" size="36rpx" @click="openShareMenu"></u-icon>
					</view>
				</cl-navbar>
			</view>
			<custom-refresher slot="refresher" :status="refresherStatus"></custom-refresher>
			<view class="loading_list">
				<!-- 顶部图片区域 -->
				<view class="top_image_container">
					<image class="top_image"
						src="https://img2.baidu.com/it/u=3018303209,1765139986&fm=253&fmt=auto&app=120&f=JPEG?w=500&h=722"
						mode="aspectFill"></image>
				</view>

				<!-- 主要内容区域 -->
				<view class="main_content">
					<!-- 小区名称 -->
					<view class="community_name">龙湖兰园天序</view>

					<!-- 标签区域 -->
					<view class="tags_container">
						<view class="tag tag_highlight">高性价比</view>
						<view class="tag">刚需优选</view>
						<view class="tag">公交直达</view>
						<view class="tag">明星户型</view>
					</view>

					<!-- 位置信息卡片 -->
					<view class="location_card">
						<view class="location_info">
							<view class="location_text">仓山区-金山公园 龙湖兰园天序</view>
							<view class="distance_text">距离您-15.6km</view>
						</view>
						<!-- <view class="map_button">
							<u-icon name="map" color="#708EBB" size="42rpx"></u-icon>
							<view class="map_text">地图</view>
						</view> -->
					</view>

					<!-- 价格信息 -->
					<view class="price_section">
						<view class="price_row">
							<view class="price_item">
								<view class="price_value">22098元/㎡</view>
								<view class="price_label">参考均价</view>
							</view>
							<view class="price_item">
								<view class="price_value">441-683万/套</view>
								<view class="price_label">参考总价</view>
							</view>
							<view class="price_item">
								<view class="price_value">3/4室</view>
								<view class="price_label">89-138㎡</view>
							</view>
						</view>
					</view>

					<!-- 分割线 -->
					<view class="divider"></view>

					<!-- 基础信息 -->
					<view class="section">
						<view class="info_grid">
							<view class="info_row">
								<view class="info_item">
									<view class="info_label">小区名称</view>
									<view class="info_value">龙湖兰园天序</view>
								</view>
								<view class="info_item">
									<view class="info_label">交易权属</view>
									<view class="info_value">商品房/安置...</view>
								</view>
							</view>
							<view class="info_row">
								<view class="info_item">
									<view class="info_label">总楼栋数</view>
									<view class="info_value">20栋</view>
								</view>
								<view class="info_item">
									<view class="info_label">建筑类型</view>
									<view class="info_value">塔板结合</view>
								</view>
							</view>
							<view class="info_row">
								<view class="info_item">
									<view class="info_label">建成年代</view>
									<view class="info_value">2022年</view>
								</view>
								<view class="info_item">
									<view class="info_label">产权年限</view>
									<view class="info_value">70年</view>
								</view>
							</view>
							<view class="info_row">
								<view class="info_item">
									<view class="info_label">
										<text>容</text>
										<text>积</text>
										<text>率</text>
									</view>
									<view class="info_value">2.2</view>
								</view>
								<view class="info_item">
									<view class="info_label">
										<text>绿</text>
										<text>化</text>
										<text>率</text>
									</view>
									<view class="info_value">30%</view>
								</view>
							</view>
						</view>
					</view>

					<!-- 分割线 -->
					<view class="divider"></view>

					<!-- 开发商信息 -->
					<view class="section">
						<view class="section_title">基础信息</view>

						<view class="info_grid">
							<view class="info_row">
								<view class="info_item">
									<view class="info_label">开发企业</view>
									<view class="info_value">龙湖地产有限公司</view>
								</view>
							</view>
							<view class="info_row">
								<view class="info_item">
									<view class="info_label">交易权属</view>
									<view class="info_value">商品房/动迁安置房</view>
								</view>
							</view>
							<view class="info_row">
								<view class="info_item">
									<view class="info_label">物业公司</view>
									<view class="info_value">福州龙湖物业服务有限公司</view>
								</view>
							</view>
							<view class="info_row">
								<view class="info_item">
									<view class="info_label">
										<text>物</text>
										<text>业</text>
										<text>费</text>
									</view>
									<view class="info_value">4.85元/月/㎡</view>
								</view>
							</view>
						</view>
					</view>

					<!-- 分割线 -->
					<view class="divider"></view>

					<!-- 小区概况 -->
					<view class="section">
						<view class="section_title">小区概况</view>
						<view class="info_grid">
							<view class="info_row">
								<view class="info_item">
									<view class="info_label">占地面积</view>
									<view class="info_value">101,300㎡</view>
								</view>
							</view>
							<view class="info_row">
								<view class="info_item">
									<view class="info_label">建筑面积</view>
									<view class="info_value">202,300㎡</view>
								</view>
							</view>
							<view class="info_row">
								<view class="info_item">
									<view class="info_label">人车分流</view>
									<view class="info_value">否</view>
								</view>
							</view>
							<view class="info_row">
								<view class="info_item">
									<view class="info_label">车位配比</view>
									<view class="info_value">1:1.6</view>
								</view>
							</view>
							<view class="info_row">
								<view class="info_item">
									<view class="info_label">
										<text>停</text>
										<text>车</text>
										<text>费</text>
									</view>
									<view class="info_value">580/月</view>
								</view>
							</view>
							<view class="info_row">
								<view class="info_item">
									<view class="info_label">总楼栋数</view>
									<view class="info_value">20栋 806户</view>
								</view>
							</view>

							<view class="info_row">
								<view class="info_item">
									<view class="info_label">供暖类型</view>
									<view class="info_value">--</view>
								</view>
							</view>
							<view class="info_row">
								<view class="info_item">
									<view class="info_label">供水类型</view>
									<view class="info_value">民用水</view>
								</view>
							</view>
							<view class="info_row">
								<view class="info_item">
									<view class="info_label">供电类型</view>
									<view class="info_value">民用电</view>
								</view>
							</view>
							<view class="info_row">
								<view class="info_item">
									<view class="info_label">供气方式</view>
									<view class="info_value">天然气</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view slot="bottom">
			</view>
		</z-paging>
		<shareMenu v-if="info.id" ref="shareMenu" :list="['复制链接', '生成二维码', 'app']" :appShare="appShare"
			@poster="generatePoster">
		</shareMenu>
	</view>
</template>

<script>
import shareMenu from "@/components/common/share_menu.vue";
import { base64ToPath, pathToBase64 } from "@/uni_modules/lime-painter/components/l-painter/utils"

export default {
	components: {
		shareMenu,
	},
	data() {
		return {
			refresherStatus: 0,
			dataList: [],
			is_bgChange: false,
			screenHeight: 0,
			info: {
				id: 1
			},
			painterShareInfo: null,

		};
	},
	computed: {
		rightIcon() {
			let icon = '';
			if (this.is_bgChange) {
				icon = 'https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/share_black.png';
			} else {
				icon = 'https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/share_white.png';
			}
			return icon
		},
		appShare() {
			let obj = {
				title: `${this.info.title}`,
				summary: `${this.info.sub_title}`,
				piclink: this.info.piclink ? this.$t.getImgUrl(this.info.piclink) : '',
			};
			return obj;
		},
	},
	onShareAppMessage(res) { //发送给朋友
		if (res.from === 'button') { // 来自页面内分享按钮
			console.log(res.target)
		}
		var shareinfo = {
			title: this.appShare.title,
			path: this.$Route.fullPath,
			imageUrl: this.appShare.piclink,
		}
		return shareinfo
	},
	onShareTimeline(res) { //分享到朋友圈
		if (res.from === 'button') { // 来自页面内分享按钮
			console.log(res.target)
		}
		var shareinfo = {
			title: this.appShare.title,
			path: this.$Route.fullPath,
			imageUrl: this.appShare.piclink,
		}
		return shareinfo
	},
	onLoad() {
		this.screenHeight = uni.getSystemInfoSync().windowHeight;
	},
	methods: {
		openShareMenu() {
			if (this.$t.checkisLogin()) {
				this.$refs.shareMenu.open();
			}
		},
		generatePoster() {
			if (this.$t.checkisLogin()) {
				uni.showLoading({
					title: "生成海报中",
				});
				let share = {
					piclink: this.$t.getImgUrl(this.info.piclink),
					title: this.info.title,
					sub_title: this.info.sub_title,
					avatar: this.$t.getImgUrl(this.userInfo.avatar),
					nickname: `我是${this.userInfo.nickname || this.userInfo.username}`,
					username: `我的推广码${this.userInfo.username}`,
					shareUrl: `${this.baseConfig.shop.wx_mobile_web}${this.$Route.fullPath}`,
					qrcode: "",
				};

				let promistArr = [
					{ iden: 'piclink', piclink: this.info.piclink },
					{ iden: 'avatar', piclink: this.userInfo.avatar },
				]
				// #ifdef H5
				//跨域处理
				let promisePiclink = async (promistArr) => {
					const files = promistArr.map(async (item, i) => {
						return new Promise((resove, reject) => {
							pathToBase64(item.piclink).then(url => {
								share[item.iden] = url;
								resove(url);
							}).catch(err => {
								reject('error');
							})
						});
					});
					return await Promise.all(files);
				}
				promisePiclink(promistArr).then((res) => {
					this.painterShareInfo = share;
				}).catch(err => {
					uni.hideLoading();
					this.$t.toast('生成海报失败')
				})
				// #endif
				// #ifndef H5
				this.painterShareInfo = share;
				// #endif
			}
		},
		onScroll(e) {
			if (e.detail.scrollTop > this.screenHeight / 4) {
				this.is_bgChange = true;
			} else {
				this.is_bgChange = false;
			}
		},

		// 页面滚动生命周期函数
		onPageScroll(e) {
			console.log('onPageScroll:', e);

			// 监听滚动到屏幕一半
			if (e.scrollTop > this.screenHeight / 2) {
				this.is_bgChange = true;
			} else {
				this.is_bgChange = false;
			}
		},
		queryList(pageNo, pageSize) {
			let params = {
				page: pageNo,
				page_size: pageSize,
			};
			// this.$api.getNews.getNewsList(params).then((res) => {
			// 	if (res.code == 200) {
			// 		this.$refs.paging.complete(res.result.data);
			// 		// this.$refs.paging.completeByNoMore(res.result, true); //:refresher-enabled="false" :show-loading-more-no-more-view="false"
			// 	} else {
			// 		this.$refs.paging.complete(false);
			// 	}
			// });
		},

	},
}
</script>

<style lang="scss" scoped>
.loading {
	width: 100%;

	.loading_list {
		width: 100%;

		// 顶部图片区域
		.top_image_container {
			position: relative;
			width: 100%;
			height: 750rpx;

			.top_image {
				width: 100%;
				height: 750rpx;
			}
		}

		// 主要内容区域
		.main_content {
			position: relative;
			top: -35rpx;
			background: #ffffff;
			border-radius: 30rpx 30rpx 0 0;

			>view {
				padding: 0 25rpx;
			}

			// 小区名称
			.community_name {
				font-size: 36rpx;
				font-weight: bold;
				color: #000000;
				line-height: 36rpx;
				padding-top: 30rpx;
			}

			// 标签区域
			.tags_container {
				@include flex-center(row, flex-start, center);
				gap: 20rpx;
				padding: 0 25rpx;
				margin-top: 20rpx;

				.tag {
					padding: 17rpx 20rpx;
					border-radius: 8rpx;
					background: #f7f7f7;
					font-size: 24rpx;
					color: #8a8a8a;

					&.tag_highlight {
						background: #f9f3ec;
						color: #b28c62;
					}
				}
			}

			// 位置信息卡片
			.location_card {
				margin: 20rpx 25rpx 0 25rpx;
				padding: 20rpx;
				background: #fafafa;
				border-radius: 20rpx;
				@include flex-center(row, space-between, center);
				box-sizing: border-box;
				background: url('https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/dw_bg.png') no-repeat center center;
				background-size: cover;
				background-size: 100% 100%;

				.location_info {
					flex: 0.7;
					@include flex-center(column, flex-start, flex-start);
					gap: 26rpx;

					.location_text {
						width: 100%;
						font-size: 26rpx;
						font-weight: bold;
						color: #000000;
						line-height: 26rpx;

						>view {
							@include text-overflow(100%, 1);
						}
					}

					.distance_text {
						font-size: 22rpx;
						color: #8a8a8a;
						line-height: 22rpx;
					}
				}

				.map_button {
					@include flex-center(column, center, center);
					gap: 8rpx;

					.map_text {
						font-size: 22rpx;
						color: #708ebb;
						line-height: 22rpx;
					}
				}
			}

			// 价格信息
			.price_section {
				padding: 30rpx 25rpx;

				.price_row {
					@include flex-center(row, space-between, flex-end);

					.price_item {
						flex: 1;
						@include flex-center(column, center, center);
						gap: 5rpx;

						.price_value {
							font-size: 30rpx;
							font-weight: bold;
							color: #000000;
							line-height: 30rpx;
						}

						.price_label {
							font-size: 24rpx;
							color: #969696;
							line-height: 24rpx;
						}
					}
				}
			}

			// 分割线
			.divider {
				width: 100%;
				height: 15rpx;
				background: #f8f8f8;
				padding: 0;
			}

			// 章节标题和信息网格
			.section {
				padding: 30rpx 25rpx;

				.section_title {
					font-size: 32rpx;
					font-weight: bold;
					color: #333333;
					line-height: 32rpx;
					margin-bottom: 25rpx;
				}

				.info_grid {
					display: flex;
					flex-direction: column;
					gap: 25rpx;

					.info_row {
						@include flex-center(row, space-between, flex-start);

						&:last-child {
							margin-bottom: 0;
						}

						.info_item {
							flex: 1;
							@include flex-center(row, flex-start, flex-start);
							gap: 30rpx;

							.info_label {
								width: 115rpx;
								font-size: 28rpx;
								color: #969696;
								line-height: 28rpx;
								display: flex;
								justify-content: space-between;
							}

							.info_value {
								flex: 1;
								@include text-overflow(100%, 1);
								font-size: 28rpx;
								color: #333333;
								line-height: 28rpx;
							}
						}
					}
				}
			}
		}
	}
}
</style>