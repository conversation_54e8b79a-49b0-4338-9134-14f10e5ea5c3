<template>
	<view class="loading">
		<z-paging ref="paging" v-model="dataList" @query="queryList" bgColor="#f7f7f7"
			:refresher-status.sync="refresherStatus">
			<view slot="top">
				<cl-navbar title="发布房源" mpWeiXinShow :autoBack="true" :fixed="false" class="custom_navbar"> </cl-navbar>
			</view>
			<custom-refresher slot="refresher" :status="refresherStatus"></custom-refresher>
			<view class="loading_list">
				<!-- 房屋图片 -->
				<view class="house_image_section">
					<view class="section_title">
						<text class="required">*</text>
						<text>房屋图片</text>
					</view>
					<view class="image_upload_area">
						<u-upload :fileList="formData.images" @afterRead="afterRead" @delete="deleteImage" :maxCount="9"
							width="160" height="160" :previewFullImage="true" uploadText="上传图片"></u-upload>
					</view>
				</view>
				<view class="divider"></view>

				<!-- 城市选择 -->
				<view class="form_item">
					<view class="item_label">
						<text class="required">*</text>
						<text>城市</text>
					</view>
					<view class="item_content" @click="selectCity">
						<text v-if="formData.city">{{ formData.city }}</text>
						<text v-else class="placeholder">请选择</text>
						<u-icon name="arrow-right" size="20"></u-icon>
					</view>
				</view>
				<view class="divider"></view>

				<!-- 小区 -->
				<view class="form_item">
					<view class="item_label">
						<text class="required">*</text>
						<text>小区</text>
					</view>
					<view class="item_content">
						<text v-if="formData.community">{{ formData.community }}</text>
						<text v-else class="placeholder">请输入小区名称</text>
						<u-icon name="arrow-right" size="20"></u-icon>
					</view>
				</view>
				<view class="divider"></view>

				<!-- 房型 -->
				<view class="form_item">
					<view class="item_label">
						<text class="required">*</text>
						<text>房型</text>
					</view>
					<view class="item_content" @click="selectHouseType">
						<text v-if="formData.houseType">{{ formData.houseType }}</text>
						<text v-else class="placeholder">请选择</text>
						<u-icon name="arrow-right" size="20"></u-icon>
					</view>
				</view>
				<view class="divider"></view>

				<!-- 房屋面积 -->
				<view class="form_item">
					<view class="item_label">
						<text class="required">*</text>
						<text>房屋面积</text>
					</view>
					<view class="item_content">
						<text v-if="formData.area">{{ formData.area }}㎡</text>
						<text v-else class="placeholder">请输入面积</text>
						<u-icon name="arrow-right" size="20"></u-icon>
					</view>
				</view>
				<view class="divider"></view>

				<!-- 楼层信息 -->
				<view class="floor_info_section">
					<view class="section_title">
						<text class="required">*</text>
						<text>楼层信息</text>
					</view>
					<view class="floor_inputs">
						<view class="floor_input_item">
							<text>所在楼层</text>
							<text v-if="formData.floor" style="color: #333333;">{{ formData.floor }}层</text>
							<text v-else class="placeholder">请输入</text>
						</view>
						<view class="floor_input_item">
							<text>总楼层</text>
							<text v-if="formData.totalFloor" style="color: #333333;">{{ formData.totalFloor }}层</text>
							<text v-else class="placeholder">请输入</text>
						</view>
					</view>
				</view>
				<view class="divider"></view>

				<!-- 户型朝向 -->
				<view class="form_item">
					<view class="item_label">
						<text class="required">*</text>
						<text>户型朝向</text>
					</view>
					<view class="item_content" @click="selectOrientation">
						<text>东南朝向</text>
						<u-icon name="arrow-right" size="20"></u-icon>
					</view>
				</view>
				<view class="divider"></view>

				<!-- 装修类型 -->
				<view class="decoration_type_section">
					<view class="section_title">
						<text class="required">*</text>
						<text>装修类型</text>
					</view>
					<view class="decoration_types">
						<view class="decoration_type_item" :class="{ active: formData.decorationType === '豪华装' }"
							@click="selectDecorationType('豪华装')">
							<text>豪华装</text>
						</view>
						<view class="decoration_type_item" :class="{ active: formData.decorationType === '精装' }"
							@click="selectDecorationType('精装')">
							<text>精装</text>
						</view>
						<view class="decoration_type_item" :class="{ active: formData.decorationType === '简装' }"
							@click="selectDecorationType('简装')">
							<text>简装</text>
						</view>
						<view class="decoration_type_item" :class="{ active: formData.decorationType === '毛坯' }"
							@click="selectDecorationType('毛坯')">
							<text>毛坯</text>
						</view>
					</view>
				</view>

				<!-- 底部留白 -->
				<view style="height: 100rpx;"></view>
			</view>
			<view slot="bottom">
				<view class="submit_button_container">
					<button class="submit_button" @click="submitForm">提交</button>
				</view>
			</view>
		</z-paging>
	</view>
</template>

<script>
export default {
	data() {
		return {
			refresherStatus: 0,
			dataList: [],
			// 表单数据
			formData: {
				images: [],
				city: '福州市',
				community: '',
				houseType: '3室2厅',
				area: '',
				floor: '',
				totalFloor: '',
				orientation: '东南朝向',
				decorationType: '豪华装'
			}
		};
	},
	computed: {},
	onLoad() { },
	methods: {
		queryList(pageNo, pageSize) {
			let params = {
				page: pageNo,
				page_size: pageSize,
			};
			// 模拟数据加载
			setTimeout(() => {
				this.$refs.paging.complete([]);
			}, 500);
		},
		// 选择城市
		selectCity() {
			// 城市选择逻辑
			console.log('选择城市');
		},
		// 选择房型
		selectHouseType() {
			// 房型选择逻辑
			console.log('选择房型');
		},
		// 选择朝向
		selectOrientation() {
			// 朝向选择逻辑
			console.log('选择朝向');
		},
		// 选择装修类型
		selectDecorationType(type) {
			this.formData.decorationType = type;
		},
		// 图片上传后的处理函数
		afterRead(event) {
			const { file } = event;
			// 此处可以调用上传API，将图片上传到服务器
			// 示例中仅添加到本地数组
			if (Array.isArray(file)) {
				// 多选情况
				file.forEach(item => {
					this.formData.images.push({
						url: item.url,
						status: 'success',
						message: '上传成功'
					});
				});
			} else {
				// 单选情况
				this.formData.images.push({
					url: file.url,
					status: 'success',
					message: '上传成功'
				});
			}
		},
		// 删除图片
		deleteImage(event) {
			const index = event.index;
			this.formData.images.splice(index, 1);
		},
		// 提交表单
		submitForm() {
			// 表单验证
			if (!this.formData.images.length) {
				uni.showToast({
					title: '请上传房屋图片',
					icon: 'none'
				});
				return;
			}
			if (!this.formData.city) {
				uni.showToast({
					title: '请选择城市',
					icon: 'none'
				});
				return;
			}
			if (!this.formData.community) {
				uni.showToast({
					title: '请输入小区名称',
					icon: 'none'
				});
				return;
			}
			if (!this.formData.houseType) {
				uni.showToast({
					title: '请选择房型',
					icon: 'none'
				});
				return;
			}
			if (!this.formData.area) {
				uni.showToast({
					title: '请输入房屋面积',
					icon: 'none'
				});
				return;
			}
			if (!this.formData.floor || !this.formData.totalFloor) {
				uni.showToast({
					title: '请输入楼层信息',
					icon: 'none'
				});
				return;
			}
			if (!this.formData.orientation) {
				uni.showToast({
					title: '请选择户型朝向',
					icon: 'none'
				});
				return;
			}
			if (!this.formData.decorationType) {
				uni.showToast({
					title: '请选择装修类型',
					icon: 'none'
				});
				return;
			}

			// 提交表单数据
			console.log('提交表单数据', this.formData);
			uni.showToast({
				title: '提交成功',
				icon: 'success'
			});
			// 这里可以添加实际的提交逻辑
		}
	},
};
</script>

<style lang="scss" scoped>
.loading {
	width: 100%;

	.loading_list {
		width: 100%;
		background-color: #f8f8f8;

		// 通用样式
		.required {
			color: #F63030;
			margin-right: 2rpx;
		}

		.placeholder {
			color: #BFBFBF;
		}

		.divider {
			width: 700rpx;
			height: 1rpx;
			background-color: #EEEEEE;
			margin: 0 auto;
		}

		.section_title {
			@include flex-center(row, flex-start, center);
			padding: 25rpx;
			font-size: 28rpx;
			font-weight: bold;
			color: #333333;
		}

		// 房屋图片区域
		.house_image_section {
			width: 100%;
			background-color: #FFFFFF;
			padding-bottom: 30rpx;

			.image_upload_area {
				width: 100%;
				padding: 0 25rpx;

				:deep(.u-upload__button) {
					width: 160rpx !important;
					height: 160rpx !important;
					background-color: #f5f5f5;
					border-radius: 8rpx;
				}

				:deep(.u-upload__wrap__preview) {
					width: 160rpx;
					height: 160rpx;
					border-radius: 8rpx;
					overflow: hidden;
				}

				:deep(.u-upload__wrap__preview__image) {
					width: 160rpx !important;
					height: 160rpx !important;
					border-radius: 8rpx;
				}
			}
		}

		// 表单项通用样式
		.form_item {
			width: 100%;
			background-color: #FFFFFF;
			@include flex-center(row, space-between, center);
			padding: 25rpx;
			box-sizing: border-box;

			.item_label {
				@include flex-center(row, flex-start, center);
				font-size: 28rpx;
				font-weight: bold;
				color: #333333;
			}

			.item_content {
				@include flex-center(row, flex-end, center);
				font-size: 28rpx;
				color: #333333;

				.u-icon {
					margin-left: 10rpx;
				}
			}
		}

		// 楼层信息区域
		.floor_info_section {
			width: 100%;
			background-color: #FFFFFF;
			padding-bottom: 30rpx;

			.floor_inputs {
				width: 100%;
				@include flex-center(row, space-between, center);
				padding: 0 25rpx;
				box-sizing: border-box;

				.floor_input_item {
					width: 48%;
					@include flex-center(row, flex-start, center);
					font-size: 28rpx;
					color: #333333;
					gap: 10rpx;
				}
			}
		}

		// 装修类型区域
		.decoration_type_section {
			width: 100%;
			background-color: #FFFFFF;
			padding-bottom: 30rpx;

			.decoration_types {
				width: 100%;
				@include flex-center(row, flex-start, center);
				padding: 0 25rpx;
				box-sizing: border-box;
				gap: 20rpx;

				.decoration_type_item {
					width: 160rpx;
					height: 56rpx;
					@include flex-center(row, center, center);
					background-color: #F8F8F8;
					border-radius: 60rpx;
					font-size: 26rpx;
					color: #333333;

					&.active {
						background-color: rgba(0, 106, 252, 0.1);
						color: #006AFC;
					}
				}
			}
		}
	}

	// 提交按钮样式
	.submit_button_container {
		width: 100%;
		padding: 20rpx 30rpx;
		background-color: #FFFFFF;
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
		z-index: 999;
		box-sizing: border-box;

		.submit_button {
			width: 100%;
			height: 88rpx;
			background-color: #006AFC;
			color: #FFFFFF;
			font-size: 32rpx;
			font-weight: bold;
			border-radius: 44rpx;
			@include flex-center(row, center, center);
		}
	}
}
</style>